use crate::models::config::{MonitorConfig, MonitorItem};
use crate::services::account::AccountService;
use crate::services::monitor::MonitorDataPool;
use crate::services::{
    ConfigService, CryptoService, EncryptedClient, LoggingService, NotificationService,
};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::atomic::{AtomicBool, AtomicUsize, Ordering};
use std::sync::Arc;
use tauri::{AppHandle, Emitter, Manager};
use tokio::sync::{RwLock, Mutex};
use tokio::time::{sleep, Duration, Instant};
use tokio::task::Join<PERSON><PERSON><PERSON>;

/// 监控状态（简化版）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MonitorStatus {
    pub is_running: bool,
    pub message: String,
    pub start_time: Option<String>,
    pub last_update_time: Option<String>, // 最后更新时间
    // 多线程统计信息
    pub items_count: usize,
    pub total_found_items: usize,
    pub execution_count: usize,
    pub total_keywords: usize,
    pub uptime_seconds: u64,
}

/// 监控数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MonitorData {
    pub items: Vec<MonitorItem>,
    pub total_count: usize,
    pub last_update: String,
}

/// 共享关键词池 - 线程安全的关键词轮询
#[derive(Debug)]
pub struct SharedKeywordPool {
    keywords: Arc<RwLock<Vec<String>>>,
    current_index: Arc<AtomicUsize>,
}

impl SharedKeywordPool {
    pub fn new(keywords: Vec<String>) -> Self {
        Self {
            keywords: Arc::new(RwLock::new(keywords)),
            current_index: Arc::new(AtomicUsize::new(0)),
        }
    }

    /// 获取下一个关键词（原子操作）
    pub async fn get_next_keyword(&self) -> Option<String> {
        let keywords = self.keywords.read().await;
        if keywords.is_empty() {
            return None;
        }

        let index = self.current_index.fetch_add(1, Ordering::Relaxed) % keywords.len();
        Some(keywords[index].clone())
    }

    /// 更新关键词列表
    pub async fn update_keywords(&self, new_keywords: Vec<String>) {
        let mut keywords = self.keywords.write().await;
        *keywords = new_keywords;
        self.current_index.store(0, Ordering::Relaxed);
    }

    /// 获取关键词数量
    pub async fn len(&self) -> usize {
        let keywords = self.keywords.read().await;
        keywords.len()
    }
}

/// 账号监控线程状态
#[derive(Debug, Clone)]
pub struct AccountThreadState {
    pub account_id: String,
    pub is_running: bool,
    pub is_paused: bool, // 验证码暂停状态
    pub last_execution: Option<Instant>,
    pub error_message: Option<String>,
    pub start_time: Instant, // 添加启动时间
}

/// 多账号调度器
pub struct MultiAccountScheduler {
    app_handle: AppHandle,
    account_service: AccountService,
    logging_service: LoggingService,
    notification_service: NotificationService,

    // 共享状态
    keyword_pool: Arc<SharedKeywordPool>,
    is_running: Arc<AtomicBool>,
    config: Arc<RwLock<MonitorConfig>>,

    // 线程管理
    thread_handles: Arc<Mutex<HashMap<String, JoinHandle<()>>>>,
    thread_states: Arc<RwLock<HashMap<String, AccountThreadState>>>,

    // 数据池（用于去重）
    data_pool: Arc<Mutex<MonitorDataPool>>,

    // 统计数据
    total_executions: Arc<AtomicUsize>,
    total_found_items: Arc<AtomicUsize>,
}

impl MultiAccountScheduler {
    /// 创建新的多账号调度器
    pub fn new(
        app_handle: AppHandle,
        account_service: AccountService,
        logging_service: LoggingService,
        notification_service: NotificationService,
        config: MonitorConfig,
    ) -> Self {
        let keywords = config.get_keywords();
        Self {
            app_handle,
            account_service,
            logging_service,
            notification_service,
            keyword_pool: Arc::new(SharedKeywordPool::new(keywords)),
            is_running: Arc::new(AtomicBool::new(false)),
            config: Arc::new(RwLock::new(config)),
            thread_handles: Arc::new(Mutex::new(HashMap::new())),
            thread_states: Arc::new(RwLock::new(HashMap::new())),
            data_pool: Arc::new(Mutex::new(MonitorDataPool::new())),
            total_executions: Arc::new(AtomicUsize::new(0)),
            total_found_items: Arc::new(AtomicUsize::new(0)),
        }
    }

    /// 启动多账号监控
    pub async fn start(&self) -> Result<(), String> {
        if self.is_running.load(Ordering::Relaxed) {
            return Err("调度器已在运行中".to_string());
        }

        // 获取所有可用账号
        let available_accounts = self.account_service.get_all_accounts().await;
        let enabled_accounts: Vec<_> = available_accounts
            .into_iter()
            .filter(|account| account.enabled && account.cookie.is_some())
            .collect();

        if enabled_accounts.is_empty() {
            return Err("没有可用的账号".to_string());
        }

        self.is_running.store(true, Ordering::Relaxed);

        // 计算启动间隔：6秒 / 账号数量
        let config = self.config.read().await;
        let base_interval = config.interval_seconds;
        let start_delay_per_account = base_interval / enabled_accounts.len() as u64;

        self.logging_service
            .info(
                "MultiAccountScheduler",
                "start",
                &format!(
                    "启动多账号监控：{} 个账号，基础间隔 {}s，启动间隔 {}s",
                    enabled_accounts.len(),
                    base_interval,
                    start_delay_per_account
                ),
            )
            .await
            .ok();

        // 启动账号监控线程
        let mut handles = self.thread_handles.lock().await;
        let mut states = self.thread_states.write().await;

        for (index, account) in enabled_accounts.iter().enumerate() {
            let thread_state = AccountThreadState {
                account_id: account.id.clone(),
                is_running: true,
                is_paused: false,
                last_execution: None,
                error_message: None,
                start_time: Instant::now(), // 记录启动时间
            };

            states.insert(account.id.clone(), thread_state);

            // 启动账号监控线程，错开启动时间
            let handle = self.spawn_account_thread(
                account.id.clone(),
                index as u64 * start_delay_per_account,
            ).await;

            handles.insert(account.id.clone(), handle);
        }

        Ok(())
    }

    /// 停止多账号监控
    pub async fn stop(&self) -> Result<(), String> {
        let stop_start_time = std::time::Instant::now();
        self.is_running.store(false, Ordering::Relaxed);

        // 等待所有线程结束，使用超时机制
        let mut handles = self.thread_handles.lock().await;
        for (account_id, handle) in handles.drain() {
            let thread_stop_start = std::time::Instant::now();

            self.logging_service
                .debug(
                    "MultiAccountScheduler",
                    "stop",
                    &format!("等待账号 {} 线程结束", account_id),
                )
                .await
                .ok();

            // 使用超时等待线程结束
            let timeout_duration = Duration::from_secs(5); // 5秒超时
            match tokio::time::timeout(timeout_duration, async {
                // 先尝试优雅停止，等待线程自然结束
                tokio::time::sleep(Duration::from_millis(100)).await;
                if !handle.is_finished() {
                    handle.abort();
                }
            }).await {
                Ok(_) => {
                    let elapsed = thread_stop_start.elapsed();
                    self.logging_service
                        .debug(
                            "MultiAccountScheduler",
                            "stop",
                            &format!("账号 {} 线程已正常结束，耗时 {:.2}秒", account_id, elapsed.as_secs_f64()),
                        )
                        .await
                        .ok();
                }
                Err(_) => {
                    let elapsed = thread_stop_start.elapsed();
                    self.logging_service
                        .warn(
                            "MultiAccountScheduler",
                            "stop",
                            &format!("账号 {} 线程停止超时，强制终止，耗时 {:.2}秒", account_id, elapsed.as_secs_f64()),
                        )
                        .await
                        .ok();
                    handle.abort();
                }
            }
        }

        // 清理线程状态
        let mut states = self.thread_states.write().await;
        states.clear();

        let total_elapsed = stop_start_time.elapsed();
        self.logging_service
            .info(
                "MultiAccountScheduler",
                "stop",
                &format!("多账号监控已停止，总耗时 {:.2}秒", total_elapsed.as_secs_f64())
            )
            .await
            .ok();

        Ok(())
    }

    /// 暂停指定账号的监控（用于验证码处理）
    pub async fn pause_account(&self, account_id: &str) -> Result<(), String> {
        let mut states = self.thread_states.write().await;
        if let Some(state) = states.get_mut(account_id) {
            state.is_paused = true;
            self.logging_service
                .info(
                    "MultiAccountScheduler",
                    "pause_account",
                    &format!("账号 {} 已暂停监控", account_id),
                )
                .await
                .ok();
        }
        Ok(())
    }

    /// 恢复指定账号的监控
    pub async fn resume_account(&self, account_id: &str) -> Result<(), String> {
        let mut states = self.thread_states.write().await;
        if let Some(state) = states.get_mut(account_id) {
            state.is_paused = false;
            state.error_message = None;
            self.logging_service
                .info(
                    "MultiAccountScheduler",
                    "resume_account",
                    &format!("账号 {} 已恢复监控", account_id),
                )
                .await
                .ok();
        }
        Ok(())
    }

    /// 生成账号监控线程
    async fn spawn_account_thread(&self, account_id: String, start_delay: u64) -> JoinHandle<()> {
        let scheduler_clone = self.clone();

        tokio::spawn(async move {
            // 错开启动时间
            if start_delay > 0 {
                sleep(Duration::from_secs(start_delay)).await;
            }

            scheduler_clone.account_monitor_loop(account_id).await;
        })
    }

    /// 单个账号的监控循环
    async fn account_monitor_loop(&self, account_id: String) {
        // 获取账号昵称用于日志显示
        let account_display_name = if let Some(account) = self.account_service.get_account(&account_id).await {
            account.metadata.get("nickname")
                .cloned()
                .unwrap_or_else(|| account.name.clone())
        } else {
            account_id.clone()
        };

        self.logging_service
            .info(
                "MultiAccountScheduler",
                "account_monitor_loop",
                &format!("账号 {} 监控线程启动", account_display_name),
            )
            .await
            .ok();

        while self.is_running.load(Ordering::Relaxed) {
            // 检查账号是否暂停
            let is_paused = {
                let states = self.thread_states.read().await;
                states.get(&account_id).map(|s| s.is_paused).unwrap_or(false)
            };

            if is_paused {
                // 账号暂停中，等待1秒后重新检查
                sleep(Duration::from_secs(1)).await;
                continue;
            }

            // 获取下一个关键词
            let keyword = match self.keyword_pool.get_next_keyword().await {
                Some(kw) => kw,
                None => {
                    // 没有关键词，等待后继续
                    sleep(Duration::from_secs(1)).await;
                    continue;
                }
            };

            // 执行监控请求
            if let Err(e) = self.perform_account_monitoring(&account_id, &keyword).await {
                self.logging_service
                    .warn(
                        "MultiAccountScheduler",
                        "account_monitor_loop",
                        &format!("账号 {} 监控出错: {}", account_display_name, e),
                    )
                    .await
                    .ok();

                // 检查是否是验证码错误或令牌过期
                if e.contains("验证码") {
                    // 发送验证码通知
                    self.send_account_verification_notification(&account_id, &account_display_name, "需要验证码验证").await;

                    // 暂停该账号
                    self.pause_account(&account_id).await.ok();

                    // 更新线程状态
                    let mut states = self.thread_states.write().await;
                    if let Some(state) = states.get_mut(&account_id) {
                        state.error_message = Some(e);
                    }
                    drop(states); // 释放锁

                    // 检查是否所有账号都已暂停
                    self.check_and_auto_stop_if_all_accounts_paused().await;
                    continue;
                } else if e.contains("令牌过期") || e.contains("重新登录") {
                    // 发送令牌过期通知
                    self.send_account_verification_notification(&account_id, &account_display_name, "令牌过期，需要重新登录").await;

                    // 暂停该账号
                    self.pause_account(&account_id).await.ok();

                    // 更新线程状态
                    let mut states = self.thread_states.write().await;
                    if let Some(state) = states.get_mut(&account_id) {
                        state.error_message = Some(e);
                    }
                    drop(states); // 释放锁

                    // 检查是否所有账号都已暂停
                    self.check_and_auto_stop_if_all_accounts_paused().await;
                    continue;
                }
            }

            // 更新执行时间
            {
                let mut states = self.thread_states.write().await;
                if let Some(state) = states.get_mut(&account_id) {
                    state.last_execution = Some(Instant::now());
                }
            }

            // 等待固定间隔，但要检查停止信号
            let config = self.config.read().await;
            let interval = config.interval_seconds;
            drop(config); // 释放锁

            // 分段等待，每秒检查一次停止信号
            for _ in 0..interval {
                if !self.is_running.load(Ordering::Relaxed) {
                    break;
                }
                sleep(Duration::from_secs(1)).await;
            }
        }

        self.logging_service
            .info(
                "MultiAccountScheduler",
                "account_monitor_loop",
                &format!("账号 {} 监控线程结束", account_display_name),
            )
            .await
            .ok();
    }

    /// 执行单个账号的监控请求
    async fn perform_account_monitoring(&self, account_id: &str, keyword: &str) -> Result<(), String> {
        // 获取账号信息
        let account = self.account_service.get_account(account_id).await
            .ok_or_else(|| format!("账号不存在: {}", account_id))?;

        let current_cookies = account.get_cookie_string()
            .ok_or_else(|| "账号没有Cookie".to_string())?;

        // 获取账号昵称用于日志显示
        let account_display_name = account.metadata.get("nickname")
            .cloned()
            .unwrap_or_else(|| account.name.clone());

        self.logging_service
            .debug(
                "MultiAccountScheduler",
                "perform_account_monitoring",
                &format!("账号 {} 执行关键词 {} 监控", account_display_name, keyword),
            )
            .await
            .ok();

        // 记录账号使用
        self.account_service
            .record_account_usage(account_id, true, None)
            .await
            .ok();

        // 执行具体的监控请求
        self.execute_monitoring_request(&account_display_name, keyword, &current_cookies).await?;

        // 更新统计
        self.total_executions.fetch_add(1, Ordering::Relaxed);

        Ok(())
    }

    /// 执行具体的监控请求
    async fn execute_monitoring_request(
        &self,
        account_display_name: &str,
        keyword: &str,
        current_cookies: &str,
    ) -> Result<(), String> {
        // 创建加密客户端
        let encrypted_client = EncryptedClient::new()
            .map_err(|e| format!("创建加密客户端失败: {}", e))?;

        // 设置Cookie
        if let Err(_e) = encrypted_client
            .set_cookies(current_cookies, "https://h5api.m.goofish.com")
            .await
        {
            // Cookie设置失败，继续执行
        }

        let config = self.config.read().await;

        // 根据关键词获取对应的价格范围
        let (min_price, max_price) = config.get_price_range_for_keyword(keyword);

        let price_filter = if min_price > 0.0 || max_price < 99999.0 {
            format!("priceRange:{},{};", min_price as i32, max_price as i32)
        } else {
            String::new()
        };

        let prop_value_str = if !price_filter.is_empty() {
            serde_json::json!({"searchFilter": price_filter})
        } else {
            serde_json::json!({})
        };

        // 构建省份筛选（简化版）
        let extra_filter_value = serde_json::json!({});

        let request_url = "https://h5api.m.goofish.com/h5/mtop.taobao.idlemtopsearch.pc.search/1.0/?v=1.0&type=originaljson&accountSite=xianyu&dataType=json&timeout=20000&api=mtop.taobao.idlemtopsearch.pc.search&sessionOption=AutoLoginOnly&spm_cnt=a21ybx.search.0.0&spm_pre=a21ybx.home.searchInput.0";

        let request_body = serde_json::json!({
            "pageNumber": 1,
            "keyword": keyword,
            "fromFilter": true,
            "rowsPerPage": 10,
            "sortValue": "desc",
            "sortField": "create",
            "customDistance": "",
            "gps": "",
            "propValueStr": prop_value_str,
            "customGps": "",
            "searchReqFromPage": "pcSearch",
            "extraFilterValue": extra_filter_value,
            "userPositionJson": "{}"
        });
        // 使用超时包装网络请求
        let request_future = encrypted_client
            .post_encrypted(&request_url, &request_body, current_cookies);

        match tokio::time::timeout(Duration::from_secs(30), request_future).await
        {
            Ok(Ok(resp)) => {
                // 验证响应
                if let Err(validation_error) = Self::validate_api_response(&resp) {
                    self.logging_service
                        .warn(
                            "MultiAccountScheduler",
                            "execute_monitoring_request",
                            &format!("账号 {} API响应验证失败: {}", account_display_name, validation_error),
                        )
                        .await
                        .ok();

                    if Self::needs_login(&resp) {
                        return Err(format!("账号 {} 令牌过期，需要重新登录", account_display_name));
                    } else if Self::needs_verification(&resp) {
                        return Err(format!("账号 {} 需要验证码验证", account_display_name));
                    } else {
                        return Err(format!("账号 {} API响应错误: {}", account_display_name, validation_error));
                    }
                }
                // 处理成功响应，解析数据
                self.process_successful_response(&resp, account_display_name, keyword).await?;

                Ok(())
            }
            Ok(Err(e)) => {
                self.logging_service
                    .warn(
                        "MultiAccountScheduler",
                        "execute_monitoring_request",
                        &format!("账号 {} 请求失败: {}", account_display_name, e),
                    )
                    .await
                    .ok();
                Err(format!("网络请求失败: {}", e))
            }
            Err(_) => {
                self.logging_service
                    .warn(
                        "MultiAccountScheduler",
                        "execute_monitoring_request",
                        &format!("账号 {} 请求超时", account_display_name),
                    )
                    .await
                    .ok();
                Err(format!("网络请求超时"))
            }
        }
    }

    /// 处理成功的API响应
    async fn process_successful_response(
        &self,
        resp: &serde_json::Value,
        account_display_name: &str,
        keyword: &str,
    ) -> Result<(), String> {
        let config = self.config.read().await;

        // 解析数据
        if let Some(data) = resp.get("data") {
            if let Some(result_list) = data.get("resultList") {
                if let Some(result_array) = result_list.as_array() {
                    let mut all_items = Vec::new();
                    for (index, result) in result_array.iter().enumerate() {
                        // 解析商品数据
                        if let Some(item) = self.parse_item_data(result, &config, keyword) {
                            all_items.push(item);
                        } else {
                            self.logging_service
                                .debug(
                                    "MultiAccountScheduler",
                                    "process_successful_response",
                                    &format!("账号 {} 第{}个结果解析失败或被过滤", account_display_name, index),
                                )
                                .await
                                .ok();
                        }
                    }

                    // 使用数据池过滤新商品
                    let new_items = {
                        let mut data_pool = self.data_pool.lock().await;
                        data_pool.filter_and_add_new_items(keyword, &all_items)
                    };
                    // 按发布时间倒序排列
                    let mut sorted_items = new_items;
                    sorted_items.sort_by(|a, b| b.publish_time.cmp(&a.publish_time));
                    self.emit_batch_items(&sorted_items).await?;

                    if !sorted_items.is_empty() {
                        // 更新统计
                        self.total_found_items.fetch_add(sorted_items.len(), Ordering::Relaxed);

                        self.logging_service
                            .debug(
                                "MultiAccountScheduler",
                                "process_successful_response",
                                &format!(
                                    "账号 {} 关键词 {} 找到 {} 个新商品",
                                    account_display_name, keyword, sorted_items.len()
                                ),
                            )
                            .await
                            .ok();
                    }
                }
            }
        }

        Ok(())
    }

    /// 获取调度器统计信息
    pub async fn get_stats(&self) -> (usize, usize, usize) {
        let executions = self.total_executions.load(Ordering::Relaxed);
        let found_items = self.total_found_items.load(Ordering::Relaxed);
        let keyword_count = self.keyword_pool.len().await;
        (executions, found_items, keyword_count)
    }

    /// 解析商品数据（复用原有的完美实现）
    fn parse_item_data(
        &self,
        result: &serde_json::Value,
        config: &MonitorConfig,
        current_keyword: &str,
    ) -> Option<MonitorItem> {
        // 获取不同数据源
        let main_data = &result["data"]["item"]["main"];
        let click_args = &main_data["clickParam"]["args"];
        let ex_content = &main_data["exContent"];

        // 从 exContent 获取基础信息
        let title = ex_content["title"].as_str().unwrap_or("");
        let area = ex_content["area"].as_str().unwrap_or("");
        let user_nick_name = ex_content["userNickName"].as_str().unwrap_or("");
        let pic_url = ex_content["picUrl"].as_str().unwrap_or("");

        // 从 clickParam.args 获取详细信息
        let item_id = click_args["id"].as_str().unwrap_or("");
        let price = click_args["price"].as_str().unwrap_or("0");
        let publish_time = click_args["publishTime"].as_str().unwrap_or("");
        let seller_id = click_args["seller_id"].as_str().unwrap_or("");
        let cat_id = click_args["catId"].as_str().unwrap_or("");

        // 从 targetUrl 获取跳转链接
        let target_url = main_data["targetUrl"].as_str().unwrap_or("");

        // 屏蔽词过滤
        for blocked_word in &config.exclude_keywords {
            if title.contains(blocked_word) {
                return None;
            }
        }

        // 拉黑卖家过滤
        for blocked_seller in &config.blocked_sellers {
            if seller_id == blocked_seller.seller_id || user_nick_name == blocked_seller.seller_name
            {
                return None;
            }
        }

        // 直接使用当前搜索的关键词
        let matched_keywords = vec![current_keyword.to_string()];

        Some(MonitorItem {
            // 基础信息
            item_id: item_id.to_string(),
            title: title.to_string(),
            price: price.to_string(),
            time: chrono::Local::now().format("%Y-%m-%d %H:%M:%S").to_string(),

            // 详细信息 (从 clickParam.args)
            publish_time: publish_time.to_string(),
            seller_id: seller_id.to_string(),
            cat_id: cat_id.to_string(),

            // 扩展信息 (从 exContent)
            area: area.to_string(),
            user_nick_name: user_nick_name.to_string(),
            pic_url: pic_url.to_string(),

            // 跳转链接
            target_url: target_url.to_string(),

            // 匹配的关键词
            matched_keywords,
        })
    }

    /// 验证API响应是否成功
    fn validate_api_response(response: &serde_json::Value) -> Result<(), String> {
        // 检查是否有错误信息
        if let Some(ret) = response.get("ret") {
            if let Some(ret_array) = ret.as_array() {
                if let Some(ret_code) = ret_array.get(0).and_then(|v| v.as_str()) {
                    if ret_code != "SUCCESS::调用成功" {
                        return Err(format!("API调用失败: {}", ret_code));
                    }
                }
            }
        }
        Ok(())
    }

    /// 检查是否需要登录
    fn needs_login(response: &serde_json::Value) -> bool {
        if let Some(ret) = response.get("ret") {
            if let Some(ret_array) = ret.as_array() {
                if let Some(ret_code) = ret_array.get(0).and_then(|v| v.as_str()) {
                    return ret_code.contains("TOKEN_EMPTY")
                        || ret_code.contains("TOKEN_EXOIRED")
                        || ret_code.contains("FAIL_SYS_TOKEN_EMPTY");
                }
            }
        }
        false
    }

    /// 检查是否需要验证码
    fn needs_verification(response: &serde_json::Value) -> bool {
        if let Some(ret) = response.get("ret") {
            if let Some(ret_array) = ret.as_array() {
                if let Some(ret_code) = ret_array.get(0).and_then(|v| v.as_str()) {
                    return ret_code.contains("FAIL_SYS_USER_VALIDATE")
                        || ret_code.contains("VERIFICATION_EXPIRED")
                        || ret_code.contains("CAPTCHA_EXPIRED");
                }
            }
        }
        false
    }

    /// 发送批量商品数据事件
    async fn emit_batch_items(&self, items: &[MonitorItem]) -> Result<(), String> {
        self.app_handle
            .emit("monitor_batch_items", items)
            .map_err(|e| format!("发送批量商品数据事件失败: {}", e))
    }

    /// 更新配置
    pub async fn update_config(&self, new_config: MonitorConfig) -> Result<(), String> {
        // 更新关键词池
        let keywords = new_config.get_keywords();
        self.keyword_pool.update_keywords(keywords).await;

        // 更新配置
        let mut config = self.config.write().await;
        *config = new_config;

        Ok(())
    }

    /// 发送账号验证通知
    async fn send_account_verification_notification(&self, account_id: &str, account_display_name: &str, reason: &str) {
        // 获取账号运行时间
        let runtime_info = {
            let states = self.thread_states.read().await;
            if let Some(state) = states.get(account_id) {
                let elapsed = state.start_time.elapsed();
                let hours = elapsed.as_secs() / 3600;
                let minutes = (elapsed.as_secs() % 3600) / 60;
                let seconds = elapsed.as_secs() % 60;

                if hours > 0 {
                    format!("{}小时{}分{}秒", hours, minutes, seconds)
                } else if minutes > 0 {
                    format!("{}分{}秒", minutes, seconds)
                } else {
                    format!("{}秒", seconds)
                }
            } else {
                "未知".to_string()
            }
        };

        let notification_id = format!("account_verification_{}", account_display_name);
        let title = "账号需要验证";
        let message = format!("账号 {} {}，本次运行时长：{}", account_display_name, reason, runtime_info);

        // 发送持久化通知（需要用户手动关闭）
        if let Err(e) = self.notification_service.send_persistent_notification(
            &notification_id,
            title,
            &message,
            Some("warning".to_string()),
        ).await {
            self.logging_service
                .warn(
                    "MultiAccountScheduler",
                    "send_account_verification_notification",
                    &format!("发送账号验证通知失败: {}", e),
                )
                .await
                .ok();
        } else {
            self.logging_service
                .info(
                    "MultiAccountScheduler",
                    "send_account_verification_notification",
                    &format!("已发送账号验证通知: {}", message),
                )
                .await
                .ok();
        }
    }

    /// 检查是否所有账号都已暂停，如果是则自动停止监控
    async fn check_and_auto_stop_if_all_accounts_paused(&self) {
        let states = self.thread_states.read().await;
        let total_accounts = states.len();
        let paused_accounts = states.values().filter(|state| state.is_paused).count();

        if total_accounts > 0 && paused_accounts == total_accounts {
            self.logging_service
                .warn(
                    "MultiAccountScheduler",
                    "check_and_auto_stop_if_all_accounts_paused",
                    "所有账号都已暂停，自动停止监控",
                )
                .await
                .ok();

            // 发送监控自动停止通知
            let notification_id = "monitor_auto_stopped";
            let title = "监控已自动停止";
            let message = "所有账号都需要验证，监控已自动停止";

            if let Err(e) = self.notification_service.send_persistent_notification(
                notification_id,
                title,
                message,
                Some("error".to_string()),
            ).await {
                self.logging_service
                    .warn(
                        "MultiAccountScheduler",
                        "check_and_auto_stop_if_all_accounts_paused",
                        &format!("发送监控停止通知失败: {}", e),
                    )
                    .await
                    .ok();
            }

            // 发送自动停止事件到前端和外层服务
            if let Err(e) = self.app_handle.emit("monitor_auto_stop_requested", ()) {
                self.logging_service
                    .warn(
                        "MultiAccountScheduler",
                        "check_and_auto_stop_if_all_accounts_paused",
                        &format!("发送自动停止事件失败: {}", e),
                    )
                    .await
                    .ok();
            }

            // 自动停止监控
            self.stop().await.ok();
        }
    }
}

impl Clone for MultiAccountScheduler {
    fn clone(&self) -> Self {
        Self {
            app_handle: self.app_handle.clone(),
            account_service: self.account_service.clone(),
            logging_service: self.logging_service.clone(),
            notification_service: self.notification_service.clone(),
            keyword_pool: Arc::clone(&self.keyword_pool),
            is_running: Arc::clone(&self.is_running),
            config: Arc::clone(&self.config),
            thread_handles: Arc::clone(&self.thread_handles),
            thread_states: Arc::clone(&self.thread_states),
            data_pool: Arc::clone(&self.data_pool),
            total_executions: Arc::clone(&self.total_executions),
            total_found_items: Arc::clone(&self.total_found_items),
        }
    }
}

/// 纯粹的监控服务 - 完全解耦，只处理监控逻辑
#[derive(Clone)]
pub struct MonitorService {
    app_handle: AppHandle,
    is_running: Arc<AtomicBool>,
    config: Arc<RwLock<MonitorConfig>>,
    data: Arc<RwLock<Vec<MonitorItem>>>,
    status: Arc<RwLock<MonitorStatus>>,
    // 新增服务依赖
    logging_service: LoggingService,
    config_service: ConfigService,
    notification_service: NotificationService,
    account_service: AccountService,
    encrypted_client: Arc<RwLock<Option<EncryptedClient>>>,
    // 多账号调度器
    scheduler: Arc<Mutex<Option<MultiAccountScheduler>>>,
}

impl MonitorService {
    /// 创建新的监控服务实例
    pub fn new(
        app_handle: AppHandle,
        logging_service: LoggingService,
        config_service: ConfigService,
        notification_service: NotificationService,
        account_service: AccountService,
    ) -> Self {
        Self {
            app_handle,
            is_running: Arc::new(AtomicBool::new(false)),
            config: Arc::new(RwLock::new(MonitorConfig::default())),
            data: Arc::new(RwLock::new(Vec::new())),
            status: Arc::new(RwLock::new(MonitorStatus {
                is_running: false,
                message: "监控已停止".to_string(),
                start_time: None,
                last_update_time: None,
                items_count: 0,
                total_found_items: 0,
                execution_count: 0,
                total_keywords: 0,
                uptime_seconds: 0,
            })),
            logging_service,
            config_service,
            notification_service,
            account_service,
            encrypted_client: Arc::new(RwLock::new(None)),
            scheduler: Arc::new(Mutex::new(None)),
        }
    }

    /// 获取app_handle的引用
    pub fn app_handle(&self) -> &AppHandle {
        &self.app_handle
    }

    /// 设置账号事件监听器
    async fn setup_account_event_listener(&self) {
        // 这里可以监听账号Cookie更新事件，自动恢复监控
        // 由于Tauri事件系统的限制，这里暂时不实现
        // 实际的恢复逻辑会在前端处理
    }

    /// 创建或获取加密客户端
    async fn get_encrypted_client(&self) -> Result<EncryptedClient, String> {
        let mut client_guard = self.encrypted_client.write().await;
        if client_guard.is_none() {
            self.logging_service
                .debug(
                    "MonitorService",
                    "get_encrypted_client",
                    "创建新的加密客户端",
                )
                .await?;

            // let client = EncryptedClient::new_with_proxy("http://127.0.0.1:9000")?;
            let client = EncryptedClient::new()?;
            *client_guard = Some(client.clone());
            Ok(client)
        } else {
            Ok(client_guard.as_ref().unwrap().clone())
        }
    }

    /// 验证API响应是否成功
    fn validate_api_response(response: &serde_json::Value) -> Result<(), String> {
        // 检查是否有错误信息
        if let Some(ret) = response.get("ret") {
            if let Some(ret_array) = ret.as_array() {
                if let Some(ret_code) = ret_array.get(0).and_then(|v| v.as_str()) {
                    if ret_code != "SUCCESS::调用成功" {
                        println!("API返回错误: {:?}", ret_code);
                        return Err(format!("API返回错误: {}", ret_code));
                    }
                }
            }
        }

        // 检查基础响应结构
        if response.get("data").is_none() {
            return Err("响应缺少data字段".to_string());
        }

        Ok(())
    }

    /// 检查响应是否需要验证码处理
    fn needs_verification(response: &serde_json::Value) -> bool {
        // 检查是否包含验证链接
        if let Some(data) = response.get("data") {
            if data.get("url").is_some() || data.get("h5url").is_some() {
                return true;
            }
        }

        // 检查错误信息是否包含验证码相关关键词
        if let Some(ret) = response.get("ret") {
            if let Some(ret_array) = ret.as_array() {
                if let Some(ret_code) = ret_array.get(0).and_then(|v| v.as_str()) {
                    if ret_code.contains("挤爆") || ret_code.contains("FAIL_SYS_USER_VALIDATE") {
                        return true;
                    }
                }
            }
        }

        false
    }

    /// 检查响应是否需要登录处理
    fn needs_login(response: &serde_json::Value) -> bool {
        // 检查错误信息是否包含"令牌过期"关键词
        if let Some(ret) = response.get("ret") {
            if let Some(ret_array) = ret.as_array() {
                if let Some(ret_code) = ret_array.get(0).and_then(|v| v.as_str()) {
                    if ret_code.contains("令牌过期") || ret_code.contains("TOKEN_EXOIRED") {
                        return true;
                    }
                }
            }
        }
        false
    }

    /// 检查验证码是否过期需要重新登录
    fn verification_expired(response: &serde_json::Value) -> bool {
        // 检查验证码过期的特定错误信息
        if let Some(ret) = response.get("ret") {
            if let Some(ret_array) = ret.as_array() {
                if let Some(ret_code) = ret_array.get(0).and_then(|v| v.as_str()) {
                    // 验证码过期或失效的错误码
                    if ret_code.contains("验证码过期")
                        || ret_code.contains("验证失败")
                        || ret_code.contains("VERIFICATION_EXPIRED")
                        || ret_code.contains("CAPTCHA_EXPIRED")
                    {
                        return true;
                    }
                }
            }
        }
        false
    }

    /// 启动监控
    pub async fn start_monitoring(&self, config: MonitorConfig) -> Result<(), String> {
        if self.is_running.load(Ordering::Relaxed) {
            return Err("监控已在运行中".to_string());
        }

        // 验证配置
        config.validate()?;

        // 更新配置
        {
            let mut current_config = self.config.write().await;
            *current_config = config.clone();
        }

        // 创建多账号调度器
        let scheduler = MultiAccountScheduler::new(
            self.app_handle.clone(),
            self.account_service.clone(),
            self.logging_service.clone(),
            self.notification_service.clone(),
            config,
        );

        // 启动调度器
        scheduler.start().await?;

        // 保存调度器实例
        {
            let mut scheduler_guard = self.scheduler.lock().await;
            *scheduler_guard = Some(scheduler);
        }

        // 统一更新状态
        self.update_status(
            true,
            "多账号监控已启动".to_string(),
            Some(chrono::Utc::now().to_rfc3339()),
        )
        .await?;

        // 启动状态检查任务
        self.start_status_check_task().await;

        Ok(())
    }

    /// 停止监控
    pub async fn stop_monitoring(&self) -> Result<(), String> {
        // 停止调度器
        {
            let mut scheduler_guard = self.scheduler.lock().await;
            if let Some(scheduler) = scheduler_guard.take() {
                scheduler.stop().await?;
            }
        }

        // 检查当前状态消息
        let current_message = {
            let status = self.status.read().await;
            status.message.clone()
        };

        // 决定新的状态消息
        let new_message = if current_message.contains("验证码") {
            current_message // 保留验证码状态
        } else {
            "监控已停止".to_string()
        };

        // 统一更新状态
        self.update_status(false, new_message, None).await?;

        Ok(())
    }

    /// 暂停指定账号（用于验证码处理）
    pub async fn pause_account(&self, account_id: &str) -> Result<(), String> {
        if let Some(scheduler) = self.scheduler.lock().await.as_ref() {
            scheduler.pause_account(account_id).await?;
        }
        Ok(())
    }

    /// 启动状态检查任务
    async fn start_status_check_task(&self) {
        let monitor_service = self.clone();

        tokio::spawn(async move {
            while monitor_service.is_running.load(Ordering::Relaxed) {
                // 每5秒检查一次调度器状态
                tokio::time::sleep(Duration::from_secs(5)).await;

                // 检查调度器是否还在运行
                let scheduler_running = {
                    let scheduler_guard = monitor_service.scheduler.lock().await;
                    if let Some(scheduler) = scheduler_guard.as_ref() {
                        scheduler.is_running.load(Ordering::Relaxed)
                    } else {
                        false
                    }
                };

                // 如果外层服务认为在运行，但调度器已停止，则更新状态
                if monitor_service.is_running.load(Ordering::Relaxed) && !scheduler_running {
                    monitor_service.logging_service
                        .info(
                            "MonitorService",
                            "status_check_task",
                            "检测到调度器已自动停止，更新服务状态",
                        )
                        .await
                        .ok();

                    // 更新服务状态
                    monitor_service.update_status(
                        false,
                        "所有账号都需要验证，监控已自动停止".to_string(),
                        None,
                    ).await.ok();

                    break;
                }
            }
        });
    }

    /// 恢复指定账号
    pub async fn resume_account(&self, account_id: &str) -> Result<(), String> {
        if let Some(scheduler) = self.scheduler.lock().await.as_ref() {
            scheduler.resume_account(account_id).await?;
        }
        Ok(())
    }

    /// 检查监控状态
    pub async fn is_running(&self) -> bool {
        // 确保返回的状态与内部状态一致
        let status = self.status.read().await;
        status.is_running
    }

    /// 获取监控状态
    pub async fn get_status(&self) -> MonitorStatus {
        let mut status = {
            let status = self.status.read().await;
            status.clone()
        };

        // 如果调度器正在运行，获取最新统计信息
        if let Some(scheduler) = self.scheduler.lock().await.as_ref() {
            let (executions, found_items, keyword_count) = scheduler.get_stats().await;
            status.execution_count = executions;
            status.total_found_items = found_items;
            status.total_keywords = keyword_count;
            status.items_count = found_items; // 简化处理，使用总数
        }

        status
    }

    /// 获取监控数据
    pub async fn get_data(&self) -> MonitorData {
        let data = self.data.read().await;
        MonitorData {
            items: data.clone(),
            total_count: data.len(),
            last_update: chrono::Utc::now().to_rfc3339(),
        }
    }

    /// 清空监控数据
    pub async fn clear_data(&self) -> Result<(), String> {
        {
            let mut data = self.data.write().await;
            data.clear();
        }

        // 发送数据清空事件
        self.emit_data_cleared().await?;

        Ok(())
    }

    /// 更新配置
    pub async fn update_config(&self, new_config: MonitorConfig) -> Result<(), String> {
        // 验证配置
        new_config.validate()?;

        // 更新配置
        {
            let mut config = self.config.write().await;
            *config = new_config.clone();
        }

        // 如果调度器正在运行，更新调度器配置
        if let Some(scheduler) = self.scheduler.lock().await.as_ref() {
            scheduler.update_config(new_config).await?;
        }

        // 发送配置更新事件
        self.emit_config_updated().await?;

        Ok(())
    }

    /// 获取当前配置
    pub async fn get_config(&self) -> MonitorConfig {
        let config = self.config.read().await;
        config.clone()
    }

    /// 监控循环（核心逻辑）
    async fn monitor_loop(&self) {
        // 初始化数据池
        let mut data_pool = MonitorDataPool::new();
        let request_url = "https://h5api.m.goofish.com/h5/mtop.taobao.idlemtopsearch.pc.search/1.0/?v=1.0&type=originaljson&accountSite=xianyu&dataType=json&timeout=20000&api=mtop.taobao.idlemtopsearch.pc.search&sessionOption=AutoLoginOnly&spm_cnt=a21ybx.search.0.0&spm_pre=a21ybx.home.searchInput.0";

        // 关键词轮询索引
        let mut keyword_index = 0;
        // 激活状态检查计数器（每10个监控周期检查一次激活状态）
        let mut activation_check_counter = 0;
        const ACTIVATION_CHECK_INTERVAL: u32 = 10;

        while self.is_running.load(Ordering::Relaxed) {
            // 定期检查激活状态（每10个监控周期检查一次）
            if activation_check_counter >= ACTIVATION_CHECK_INTERVAL {
                // 通过 AppHandle 获取 AuthService 并检查激活状态
                if let Some(auth_service) =
                    self.app_handle.try_state::<crate::services::AuthService>()
                {
                    match auth_service.check_activation_status().await {
                        Ok(is_activated) => {
                            if !is_activated {
                                self.logging_service
                                    .warn(
                                        "MonitorService",
                                        "monitor_loop",
                                        "激活状态失效，停止监控",
                                    )
                                    .await
                                    .ok();

                                // 更新监控状态为激活失效
                                if let Err(update_err) = self
                                    .update_status(
                                        false,
                                        "监控已停止 - 激活状态失效".to_string(),
                                        None,
                                    )
                                    .await
                                {
                                    eprintln!("更新激活状态失效状态失败: {}", update_err);
                                }

                                break; // 退出监控循环
                            }
                        }
                        Err(e) => {
                            self.logging_service
                                .warn(
                                    "MonitorService",
                                    "monitor_loop",
                                    &format!("激活状态检查失败，停止监控: {}", e),
                                )
                                .await
                                .ok();

                            // 更新监控状态为激活检查失败
                            if let Err(update_err) = self
                                .update_status(false, "监控已停止 - 激活检查失败".to_string(), None)
                                .await
                            {
                                eprintln!("更新激活检查失败状态失败: {}", update_err);
                            }

                            break; // 退出监控循环
                        }
                    }
                }
                activation_check_counter = 0; // 重置计数器
            }
            activation_check_counter += 1;
            let config = {
                let config = self.config.read().await;
                config.clone()
            };

            let keywords = config.get_keywords();
            if keywords.is_empty() {
                // 如果没有关键词，等待后继续
                sleep(Duration::from_secs(config.interval_seconds)).await;
                continue;
            }

            // 获取当前要处理的关键词（轮询）
            let current_keyword = &keywords[keyword_index % keywords.len()];

            // 处理单个关键词
            if let Err(e) = self
                .perform_single_keyword_monitoring(
                    &config,
                    &mut data_pool,
                    &request_url,
                    current_keyword,
                )
                .await
            {
                eprintln!("监控循环错误: {}", e);

                // 检查是否是验证码错误
                if e.contains("验证码") {
                    // 验证码错误，停止监控并退出循环
                    self.is_running.store(false, Ordering::Relaxed);
                    if let Err(update_err) = self
                        .update_status(false, "监控已停止 - 需要验证码".to_string(), None)
                        .await
                    {
                        eprintln!("更新验证码状态失败: {}", update_err);
                    }

                    // 发送错误事件到前端
                    if let Err(emit_err) = self.emit_error(&e).await {
                        eprintln!("发送验证码错误事件失败: {}", emit_err);
                    }

                    break; // 退出监控循环
                } else if e.contains("令牌过期") {
                    // 令牌过期，停止监控并退出循环
                    self.is_running.store(false, Ordering::Relaxed);
                    if let Err(update_err) = self
                        .update_status(false, "监控已停止 - 令牌过期".to_string(), None)
                        .await
                    {
                        eprintln!("更新令牌过期状态失败: {}", update_err);
                    }

                    // 发送错误事件到前端
                    if let Err(emit_err) = self.emit_error(&e).await {
                        eprintln!("发送令牌过期错误事件失败: {}", emit_err);
                    }

                    break; // 退出监控循环
                } else {
                    // 其他错误
                    {
                        let mut status = self.status.write().await;
                        status.message = format!("监控错误: {}", e);
                    }

                    // 发送错误事件
                    if let Err(emit_err) = self.emit_error(&e).await {
                        eprintln!("发送错误事件失败: {}", emit_err);
                    }
                }

                // 出错时也要移动到下一个关键词，避免一直卡在同一个关键词上
                keyword_index += 1;
            } else {
                // 成功处理，移动到下一个关键词
                keyword_index += 1;
            }

            // 定期清理数据池内存（每10次请求清理一次）
            if keyword_index % 10 == 0 && data_pool.cleanup_if_needed() {
                let (keyword_count, total_items) = data_pool.get_stats();
                self.logging_service
                    .info(
                        "MonitorService",
                        "monitor_loop",
                        &format!(
                            "数据池清理完成，当前监控 {} 个关键词，保存 {} 个商品ID",
                            keyword_count, total_items
                        ),
                    )
                    .await
                    .ok();
            }

            // 更新最后更新时间
            {
                let mut status = self.status.write().await;
                status.last_update_time = Some(chrono::Utc::now().to_rfc3339());
            }

            // 等待指定间隔后处理下一个关键词
            sleep(Duration::from_secs(config.interval_seconds)).await;
        }

        // 监控循环结束，检查当前状态消息
        let current_message = {
            let status = self.status.read().await;
            status.message.clone()
        };

        // 决定新的状态消息
        let new_message = if current_message.contains("验证码") {
            current_message // 保留验证码状态
        } else {
            "监控已停止".to_string()
        };

        // 统一更新状态
        if let Err(e) = self.update_status(false, new_message, None).await {
            eprintln!("更新监控状态失败: {}", e);
        }
    }

    /// 执行单个关键词的监控
    async fn perform_single_keyword_monitoring(
        &self,
        config: &MonitorConfig,
        data_pool: &mut MonitorDataPool,
        request_url: &str,
        keyword: &str,
    ) -> Result<(), String> {
        if !self.is_running.load(Ordering::Relaxed) {
            return Ok(());
        }

        // 获取下一个可用账号
        let account_selection = match self.account_service.get_next_available_account().await {
            Ok(selection) => selection,
            Err(e) => {
                // 没有可用账号，直接返回错误
                return Err(format!("没有可用账号: {}", e));
            }
        };

        let current_cookies = match account_selection.account.get_cookie_string() {
            Some(cookies) => cookies,
            None => {
                // 记录账号使用失败
                self.account_service
                    .record_account_usage(
                        &account_selection.account.id,
                        false,
                        Some("账号没有Cookie".to_string()),
                    )
                    .await
                    .ok();
                return Ok(());
            }
        };

        self.logging_service
            .debug(
                "MonitorService",
                "process_keyword_request",
                &format!(
                    "使用账号: {} (ID: {})",
                    account_selection.account.name, account_selection.account.id
                ),
            )
            .await
            .ok();

        // 使用选中的账号处理关键词请求
        self.process_keyword_with_cookie(
            keyword,
            config,
            &current_cookies,
            Some(account_selection.account.id.clone()),
            data_pool,
            request_url,
        )
        .await
    }

    /// 使用指定Cookie处理关键词请求
    async fn process_keyword_with_cookie(
        &self,
        keyword: &str,
        config: &MonitorConfig,
        current_cookies: &str,
        account_id: Option<String>,
        data_pool: &mut MonitorDataPool,
        request_url: &str,
    ) -> Result<(), String> {
        // 获取加密客户端
        let encrypted_client = self.get_encrypted_client().await?;

        // 更新加密客户端的cookie
        if let Err(_e) = encrypted_client
            .set_cookies(&current_cookies, "https://h5api.m.goofish.com")
            .await
        {
            // Cookie设置失败，继续执行
        }

        // 根据关键词获取对应的价格范围
        let (min_price, max_price) = config.get_price_range_for_keyword(keyword);

        let price_filter = if min_price > 0.0 || max_price < 99999.0 {
            format!("priceRange:{},{};", min_price as i32, max_price as i32)
        } else {
            String::new()
        };

        let prop_value_str = if !price_filter.is_empty() {
            serde_json::json!({"searchFilter": price_filter})
        } else {
            serde_json::json!({})
        };

        // 构建省份筛选的 extraFilterValue
        let extra_filter_value = self.build_extra_filter_value(&config).await;

        let request_body = serde_json::json!({
            "pageNumber": 1, // 固定请求第1页（最新数据）
            "keyword": keyword,
            "fromFilter": true,
            "rowsPerPage": 10,
            "sortValue": "desc",
            "sortField": "create",
            "customDistance": "",
            "gps": "",
            "propValueStr": prop_value_str,
            "customGps": "",
            "searchReqFromPage": "pcSearch",
            "extraFilterValue": extra_filter_value,
            "userPositionJson": "{}"
        });

        self.logging_service
            .debug(
                "MonitorService",
                "monitor_loop",
                &format!("开始请求关键词: {} 最新数据", keyword),
            )
            .await
            .ok();

        match encrypted_client
            .post_encrypted(&request_url, &request_body, &current_cookies)
            .await
        {
            Ok(resp) => {
                // 验证响应是否成功
                if let Err(validation_error) = Self::validate_api_response(&resp) {
                    // API调用返回了错误响应，检查错误类型
                    if Self::needs_login(&resp) {
                        self.logging_service
                            .warn("MonitorService", "需要登录", "检测到令牌过期，需要重新登录")
                            .await
                            .ok();

                        // 更新监控状态
                        if let Err(update_err) = self
                            .update_status(false, "监控已暂停 - 令牌过期".to_string(), None)
                            .await
                        {
                            eprintln!("更新登录状态失败: {}", update_err);
                        }

                        // 如果有账号ID，打开该账号的更新Cookie窗口，5秒后自动关闭
                        if let Some(account_id) = &account_id {
                            match self
                                .account_service
                                .reuse_login_session_for_account(account_id)
                                .await
                            {
                                Ok(_session_id) => {
                                    // 然后打开更新Cookie窗口
                                    let app_controller =
                                        self.app_handle.state::<crate::business::AppController>();
                                    let goldfish_business = app_controller.goldfish_business();

                                    match goldfish_business.update_account_cookie(account_id).await
                                    {
                                        Ok(_) => {
                                            println!(
                                                "✅ 令牌过期，已为账号 {} 打开更新Cookie窗口",
                                                account_id
                                            );
                                            // 令牌过期的情况下，让用户手动处理，不自动关闭
                                            return Err(
                                                "令牌过期，请在弹出的窗口中重新登录".to_string()
                                            );
                                        }
                                        Err(e) => {
                                            eprintln!("打开账号更新窗口失败: {}", e);
                                            return Err("令牌过期处理失败".to_string());
                                        }
                                    }
                                }
                                Err(e) => {
                                    eprintln!("打开账号更新窗口失败: {}", e);
                                    return Err("令牌过期处理失败".to_string());
                                }
                            }
                        } else {
                            return Err("令牌过期但无法确定账号".to_string());
                        }
                    } else if Self::needs_verification(&resp) {
                        self.logging_service
                            .warn("MonitorService", "需要验证码", "检测到需要验证码验证")
                            .await
                            .ok();

                        // 更新监控状态
                        if let Err(update_err) = self
                            .update_status(false, "监控已暂停 - 需要验证码".to_string(), None)
                            .await
                        {
                            eprintln!("更新验证码状态失败: {}", update_err);
                        }

                        // 如果有账号ID，打开该账号的更新Cookie窗口
                        if let Some(account_id) = &account_id {
                            // 先获取或创建会话
                            match self
                                .account_service
                                .reuse_login_session_for_account(account_id)
                                .await
                            {
                                Ok(_session_id) => {
                                    // 然后打开更新Cookie窗口
                                    let app_controller =
                                        self.app_handle.state::<crate::business::AppController>();
                                    let goldfish_business = app_controller.goldfish_business();

                                    match goldfish_business.update_account_cookie(account_id).await
                                    {
                                        Ok(_) => {
                                            println!(
                                                "✅ 验证码错误，已为账号 {} 打开更新Cookie窗口",
                                                account_id
                                            );
                                            // 等待用户处理验证码
                                            return Err("需要用户处理验证码".to_string());
                                        }
                                        Err(e) => {
                                            eprintln!("打开账号更新窗口失败: {}", e);
                                            return Err("验证码处理失败".to_string());
                                        }
                                    }
                                }
                                Err(e) => {
                                    eprintln!("创建会话失败: {}", e);
                                    return Err("验证码处理失败".to_string());
                                }
                            }
                        } else {
                            return Err("验证码错误但无法确定账号".to_string());
                        }
                    } else {
                        // 记录账号使用失败
                        if let Some(account_id) = &account_id {
                            self.account_service
                                .record_account_usage(
                                    account_id,
                                    false,
                                    Some(validation_error.clone()),
                                )
                                .await
                                .ok();
                        }
                        // 其他类型的错误，不需要特殊处理
                        return Err(validation_error);
                    }
                }

                // 响应验证通过，安全地解析数据
                if let Some(data) = resp.get("data") {
                    if let Some(result_list) = data.get("resultList") {
                        if let Some(result_array) = result_list.as_array() {
                            let mut all_items = Vec::new();

                            for result in result_array {
                                // 解析商品数据
                                if let Some(item) = self.parse_item_data(result, config, keyword) {
                                    all_items.push(item);
                                }
                            }

                            // 使用数据池过滤新商品
                            let new_items = data_pool.filter_and_add_new_items(keyword, &all_items);

                            // 按发布时间倒序排列
                            let mut sorted_items = new_items;
                            sorted_items.sort_by(|a, b| b.publish_time.cmp(&a.publish_time));

                            // 总是推送到前端（即使是空数组），这样前端可以统计执行次数
                            if let Err(e) = self.emit_batch_items(&sorted_items).await {
                                self.logging_service
                                    .debug(
                                        "MonitorService",
                                        "emit_batch_data",
                                        &format!("批量推送监控数据到前端失败: {}", e),
                                    )
                                    .await
                                    .ok();
                            }

                            // 处理新商品（只有当有新商品时才执行存储和通知）
                            if sorted_items.len() > 0 {
                                self.logging_service
                                    .info(
                                        "MonitorService",
                                        "发现新商品",
                                        &format!(
                                            "关键词 {} 发现 {} 个新商品",
                                            keyword,
                                            sorted_items.len()
                                        ),
                                    )
                                    .await
                                    .ok();

                                // 添加到存储
                                for item in &sorted_items {
                                    self.add_item_to_storage(item.clone()).await?;
                                }

                                // 立即发送钉钉通知（每个商品单独发送）
                                let current_config = self.config_service.get_config().await;
                                if current_config.dingtalk_enabled
                                    && !current_config.dingtalk_hooks.is_empty()
                                {
                                    for item in &sorted_items {
                                        match self
                                            .notification_service
                                            .send_item_notification(
                                                &current_config.dingtalk_hooks,
                                                item,
                                            )
                                            .await
                                        {
                                            Ok(_) => {}
                                            Err(e) => {
                                                self.logging_service
                                                    .debug(
                                                        "MonitorService",
                                                        "dingtalk_notify",
                                                        &format!("钉钉推送失败: {}", e),
                                                    )
                                                    .await
                                                    .ok();
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                // 记录账号使用成功
                if let Some(account_id) = &account_id {
                    self.account_service
                        .record_account_usage(account_id, true, None)
                        .await
                        .ok();
                }
            }
            Err(e) => {
                // 记录账号使用失败
                if let Some(account_id) = &account_id {
                    self.account_service
                        .record_account_usage(account_id, false, Some(e.clone()))
                        .await
                        .ok();
                }
                return Err(format!("请求失败: {}", e));
            }
        }

        Ok(())
    }

    /// 模拟获取数据
    async fn simulate_fetch_data(
        &self,
        keyword: &str,
        _config: &MonitorConfig,
    ) -> Result<Vec<MonitorItem>, String> {
        // 这里应该实现真实的数据获取逻辑
        // 为了演示，我们返回模拟数据

        let item = MonitorItem {
            item_id: format!("item_{}", uuid::Uuid::new_v4()),
            title: format!("模拟商品 - {}", keyword),
            price: "100.00".to_string(),
            time: chrono::Utc::now().to_rfc3339(),
            publish_time: chrono::Utc::now().to_rfc3339(),
            seller_id: "seller123".to_string(),
            cat_id: "cat456".to_string(),
            area: "北京".to_string(),
            user_nick_name: "测试卖家".to_string(),
            pic_url: "https://example.com/pic.jpg".to_string(),
            target_url: "https://example.com/item".to_string(),
            matched_keywords: vec![keyword.to_string()],
        };

        Ok(vec![item])
    }

    /// 统一更新状态并发送事件
    async fn update_status(
        &self,
        is_running: bool,
        message: String,
        start_time: Option<String>,
    ) -> Result<(), String> {
        // 同步更新所有状态
        self.is_running.store(is_running, Ordering::Relaxed);

        {
            let mut status = self.status.write().await;
            status.is_running = is_running;
            status.message = message;
            status.start_time = start_time;
            status.last_update_time = Some(chrono::Utc::now().to_rfc3339());
        }

        // 发送状态更新事件
        self.emit_status_changed().await
    }

    /// 发送状态变化事件
    async fn emit_status_changed(&self) -> Result<(), String> {
        let status = self.get_status().await;

        self.app_handle
            .emit("monitor_status_changed", &status)
            .map_err(|e| format!("发送状态变化事件失败: {}", e))
    }

    /// 发送新数据事件（单个商品）
    async fn emit_new_item(&self, item: &MonitorItem) -> Result<(), String> {
        self.app_handle
            .emit("monitor_new_item", item)
            .map_err(|e| format!("发送新数据事件失败: {}", e))
    }

    /// 发送批量数据事件（多个商品）
    async fn emit_batch_items(&self, items: &[MonitorItem]) -> Result<(), String> {
        self.app_handle
            .emit("monitor_batch_items", items)
            .map_err(|e| format!("发送批量数据事件失败: {}", e))
    }

    /// 发送数据清空事件
    async fn emit_data_cleared(&self) -> Result<(), String> {
        self.app_handle
            .emit("monitor_data_cleared", ())
            .map_err(|e| format!("发送数据清空事件失败: {}", e))
    }

    /// 发送配置更新事件
    async fn emit_config_updated(&self) -> Result<(), String> {
        let config = self.get_config().await;
        self.app_handle
            .emit("monitor_config_updated", &config)
            .map_err(|e| format!("发送配置更新事件失败: {}", e))
    }

    /// 发送错误事件
    async fn emit_error(&self, error: &str) -> Result<(), String> {
        self.app_handle
            .emit("monitor_error", error)
            .map_err(|e| format!("发送错误事件失败: {}", e))
    }

    /// 解析商品数据
    fn parse_item_data(
        &self,
        result: &serde_json::Value,
        config: &MonitorConfig,
        current_keyword: &str,
    ) -> Option<MonitorItem> {
        // 获取不同数据源
        let main_data = &result["data"]["item"]["main"];
        let click_args = &main_data["clickParam"]["args"];
        let ex_content = &main_data["exContent"];

        // 从 exContent 获取基础信息
        let title = ex_content["title"].as_str().unwrap_or("");
        let area = ex_content["area"].as_str().unwrap_or("");
        let user_nick_name = ex_content["userNickName"].as_str().unwrap_or("");
        let pic_url = ex_content["picUrl"].as_str().unwrap_or("");

        // 从 clickParam.args 获取详细信息
        let item_id = click_args["id"].as_str().unwrap_or("");
        let price = click_args["price"].as_str().unwrap_or("0");
        let publish_time = click_args["publishTime"].as_str().unwrap_or("");
        let seller_id = click_args["seller_id"].as_str().unwrap_or("");
        let cat_id = click_args["catId"].as_str().unwrap_or("");

        // 从 targetUrl 获取跳转链接
        let target_url = main_data["targetUrl"].as_str().unwrap_or("");

        // 屏蔽词过滤
        for blocked_word in &config.exclude_keywords {
            if title.contains(blocked_word) {
                return None;
            }
        }

        // 拉黑卖家过滤
        for blocked_seller in &config.blocked_sellers {
            if seller_id == blocked_seller.seller_id || user_nick_name == blocked_seller.seller_name
            {
                return None;
            }
        }

        // 直接使用当前搜索的关键词
        let matched_keywords = vec![current_keyword.to_string()];

        Some(MonitorItem {
            // 基础信息
            item_id: item_id.to_string(),
            title: title.to_string(),
            price: price.to_string(),
            time: chrono::Local::now().format("%Y-%m-%d %H:%M:%S").to_string(),

            // 详细信息 (从 clickParam.args)
            publish_time: publish_time.to_string(),
            seller_id: seller_id.to_string(),
            cat_id: cat_id.to_string(),

            // 扩展信息 (从 exContent)
            area: area.to_string(),
            user_nick_name: user_nick_name.to_string(),
            pic_url: pic_url.to_string(),

            // 跳转链接
            target_url: target_url.to_string(),

            // 匹配的关键词
            matched_keywords,
        })
    }

    /// 添加商品到存储（不推送到前端）
    async fn add_item_to_storage(&self, item: MonitorItem) -> Result<(), String> {
        // 添加到数据存储
        {
            let mut data = self.data.write().await;
            data.push(item.clone());

            // 保持数据量限制
            let config = self.config.read().await;
            let limit = config.display_limit as usize;
            if data.len() > limit {
                data.truncate(limit);
            }
        }

        // 不再维护后端统计，由前端自己计算

        Ok(())
    }

    /// 处理验证码挑战
    async fn handle_verification_challenge(
        verification_url: &str,
        app_handle: &AppHandle,
        logging_service: &crate::services::LoggingService,
    ) -> Result<String, String> {
        logging_service
            .info(
                "MonitorService",
                "handle_verification_challenge",
                "开始处理验证码挑战",
            )
            .await
            .ok();

        // 从状态管理中获取 BrowserService
        let browser_service: tauri::State<crate::services::BrowserService> = app_handle.state();

        // 创建验证窗口配置
        let window_config = crate::services::browser::BrowserWindowConfig::default()
            .with_title("闲鱼验证码 - 请完成验证后关闭窗口")
            .with_size(600.0, 600.0);

        // 打开验证窗口，只允许访问 goofish.com 相关域名
        let allowed_domains = vec!["*.goofish.com".to_string()];
        let browser_window = browser_service
            .open_browser_with_domains(
                verification_url,
                Some("goofish_verification".to_string()),
                Some(window_config),
                Some(allowed_domains),
            )
            .await?;

        logging_service
            .info(
                "MonitorService",
                "handle_verification_challenge",
                &format!("已打开验证窗口: {}", browser_window.label()),
            )
            .await
            .ok();

        // 等待用户完成验证并关闭窗口
        let (tx, rx) = tokio::sync::oneshot::channel::<String>();
        let tx = std::sync::Arc::new(std::sync::Mutex::new(Some(tx)));

        // 注册窗口关闭回调
        let window_label = browser_window.label().to_string();
        let logging_service_clone = logging_service.clone();
        println!("🔍 开始整准备添加回调");
        println!("🔍 窗口标签: {}", window_label);

        // 检查回调池状态
        let pool = crate::utils::get_window_callback_pool();
        let callback_count_before = pool.get_callback_count(&window_label).await;
        println!(
            "🔍 添加回调前，窗口 {} 的回调数量: {}",
            window_label, callback_count_before
        );

        crate::utils::add_close_requested_callback(
            &window_label,
            move |window| {
                let tx = tx.clone();
                let logging_service = logging_service_clone.clone();
                let window = window.clone();
                println!("🔍 回调被触发！窗口标签: {}", window.label());

                Box::pin(async move {
                    println!("🔍 进入异步回调，窗口标签: {}", window.label());
                    // 严格检查：只处理验证窗口
                    if window.label() != "goofish_verification" {
                        println!(
                            "🔍 窗口标签不匹配 '{}' != 'goofish_verification'，跳过处理",
                            window.label()
                        );
                        return Ok(crate::utils::CallbackResult::Continue);
                    }
                    println!("🔍 验证窗口关闭请求确认，开始提取Cookie");
                    if let Err(e) = logging_service
                        .info(
                            "MonitorService",
                            "verification_close_requested",
                            &format!("验证窗口 {} 请求关闭，开始提取Cookie", window.label()),
                        )
                        .await
                    {
                        eprintln!("日志记录失败: {}", e);
                    }

                    // 获取Cookie
                    let cookies_result = window.cookies();
                    let mut cookie_string = String::new();

                    match cookies_result {
                        Ok(cookies) => {
                            println!("🍪 成功提取了 {} 个Cookie", cookies.len());

                            // 过滤验证相关的Cookie
                            let verification_cookies: Vec<_> = cookies
                                .into_iter()
                                .filter(|cookie| {
                                    let name = cookie.name().to_lowercase();
                                    // 保留所有可能相关的cookie
                                    name.contains("session")
                                        || name.contains("token")
                                        || name.contains("auth")
                                        || name.contains("login")
                                        || name.contains("user")
                                        || name.contains("sid")
                                        || name.contains("_tb_token_")
                                        || name.contains("cookie2")
                                        || name.contains("t")
                                        || name.contains("x5sec")
                                        || name.contains("sec")
                                        || name.contains("verify")
                                })
                                .map(|cookie| format!("{}={}", cookie.name(), cookie.value()))
                                .collect();

                            if !verification_cookies.is_empty() {
                                cookie_string = verification_cookies.join("; ");
                                println!("✅ 成功获取验证Cookie: {}", cookie_string);
                            } else {
                                println!("⚠️ 未找到验证相关Cookie");
                            }
                        }
                        Err(e) => {
                            println!("❌ Cookie提取失败: {}", e);
                        }
                    }

                    // 发送结果
                    if let Some(sender) = tx.lock().unwrap().take() {
                        let _ = sender.send(cookie_string);
                    }

                    // 手动关闭窗口
                    if let Err(e) = window.close() {
                        eprintln!("手动关闭窗口失败: {}", e);
                    }

                    Ok(crate::utils::CallbackResult::PreventAndHandle)
                })
            },
            true, // 阻止关闭
            "闲鱼验证窗口Cookie提取回调",
        )
        .await;

        // 检查回调是否成功注册
        let callback_count_after = pool.get_callback_count(&window_label).await;
        println!(
            "🔍 添加回调后，窗口 {} 的回调数量: {}",
            window_label, callback_count_after
        );

        // 检查是否会阻止关闭
        let should_prevent = pool.should_prevent_close_sync(&window_label);
        println!(
            "🔍 窗口 {} 是否会阻止关闭: {}",
            window_label, should_prevent
        );

        // 等待用户完成验证
        match rx.await {
            Ok(cookies) => {
                logging_service
                    .info(
                        "MonitorService",
                        "handle_verification_challenge",
                        "验证码处理完成",
                    )
                    .await
                    .ok();
                Ok(cookies)
            }
            Err(_) => Err("验证码处理超时或被取消".to_string()),
        }
    }

    /// 处理登录流程并更新Cookie
    async fn handle_login_flow(
        app_handle: &AppHandle,
        logging_service: &crate::services::LoggingService,
        config_service: &ConfigService,
    ) -> Result<String, String> {
        println!("🔑 开始处理登录流程");

        logging_service
            .info("MonitorService", "handle_login_flow", "开始处理登录流程")
            .await
            .ok();

        // 获取GoldfishBusiness实例来处理登录
        let app_controller = app_handle.state::<crate::business::AppController>();
        let goldfish_business = app_controller.goldfish_business();

        // 调用登录流程
        match goldfish_business.handle_goldfish_login().await {
            Ok(_) => {
                println!("✅ 登录窗口已打开，等待用户完成登录");
                logging_service
                    .info(
                        "MonitorService",
                        "handle_login_flow",
                        "登录窗口已打开，等待用户完成登录",
                    )
                    .await
                    .ok();

                // 等待一段时间让用户完成登录
                // 实际应用中可以通过事件监听或轮询来检测登录完成
                tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;

                // TODO: 从账号管理系统获取Cookie而不是配置
                let error_msg = "登录功能已迁移到账号管理系统";
                println!("⚠️ {}", error_msg);
                logging_service
                    .warn("MonitorService", "handle_login_flow", error_msg)
                    .await
                    .ok();
                Err(error_msg.to_string())
            }
            Err(e) => {
                let error_msg = format!("打开登录窗口失败: {}", e);
                println!("❌ {}", error_msg);
                logging_service
                    .error("MonitorService", "handle_login_flow", &error_msg)
                    .await
                    .ok();
                Err(error_msg)
            }
        }
    }

    /// 合并新的Cookie到现有Cookie并保存到配置
    async fn merge_and_save_cookies(&self, new_cookies: &str) -> Result<String, String> {
        println!("🔄 开始合并Cookie");

        // TODO: 从账号管理系统获取Cookie而不是配置
        let existing_cookies = String::new(); // 临时空字符串

        println!(
            "📋 现有Cookie: {}",
            if existing_cookies.is_empty() {
                "无"
            } else {
                &existing_cookies
            }
        );
        println!("📋 新Cookie: {}", new_cookies);

        // 解析现有Cookie到HashMap
        let mut cookie_map = std::collections::HashMap::new();

        if !existing_cookies.is_empty() {
            for cookie_pair in existing_cookies.split(';') {
                let cookie_pair = cookie_pair.trim();
                if let Some((name, value)) = cookie_pair.split_once('=') {
                    cookie_map.insert(name.trim().to_string(), value.trim().to_string());
                }
            }
        }

        // 解析新Cookie并合并（新Cookie优先）
        for cookie_pair in new_cookies.split(';') {
            let cookie_pair = cookie_pair.trim();
            if let Some((name, value)) = cookie_pair.split_once('=') {
                let name = name.trim().to_string();
                let value = value.trim().to_string();

                if !name.is_empty() && !value.is_empty() {
                    cookie_map.insert(name, value);
                }
            }
        }

        // 重新组装Cookie字符串
        let merged_cookies: Vec<String> = cookie_map
            .iter()
            .map(|(name, value)| format!("{}={}", name, value))
            .collect();
        let merged_cookies_str = merged_cookies.join("; ");

        // Cookie合并完成

        Ok(merged_cookies_str)
    }

    /// 构建 extraFilterValue 字符串，包含省份筛选信息
    async fn build_extra_filter_value(&self, config: &MonitorConfig) -> String {
        if config.selected_provinces.is_empty() {
            return "{}".to_string();
        }

        // 构建省份列表，格式为 [{"province":"省份名"}]
        let division_list: Vec<serde_json::Value> = config
            .selected_provinces
            .iter()
            .map(|province| {
                serde_json::json!({
                    "province": province.name
                })
            })
            .collect();

        // 构建完整的 extraFilterValue 对象
        let extra_filter = serde_json::json!({
            "divisionList": division_list,
            "excludeMultiPlacesSellers": "0",
            "extraDivision": ""
        });

        // 转换为字符串
        extra_filter.to_string()
    }
}
