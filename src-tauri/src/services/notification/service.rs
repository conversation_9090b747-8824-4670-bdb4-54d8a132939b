use serde::{Deserialize, Serialize};
use std::collections::{HashMap, VecDeque};
use std::sync::Arc;
use tauri::{AppHandle, Emitter};
use tokio::sync::RwLock;

/// 通知渠道类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum NotificationChannel {
    DingTalk { webhook_url: String },
    Email { smtp_config: EmailConfig },
    WebPush { endpoint: String },
    System, // 系统通知
}

/// 邮件配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EmailConfig {
    pub smtp_server: String,
    pub smtp_port: u16,
    pub username: String,
    pub password: String,
    pub from_email: String,
}

/// 通知消息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NotificationMessage {
    pub title: String,
    pub content: String,
    pub level: NotificationLevel,
    pub timestamp: String,
    pub metadata: HashMap<String, String>,
}

/// 通知级别
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub enum NotificationLevel {
    Info,
    Warning,
    Error,
    Success,
}

/// 钉钉 Hook 频率限制器
#[derive(Debug)]
struct HookRateLimiter {
    webhook_url: String,
    push_times: VecDeque<chrono::DateTime<chrono::Utc>>,
    max_per_minute: usize,
}

impl HookRateLimiter {
    fn new(webhook_url: String) -> Self {
        Self {
            webhook_url,
            push_times: VecDeque::new(),
            max_per_minute: 20, // 每分钟最多20次
        }
    }

    /// 检查是否可以推送
    fn can_push(&mut self) -> bool {
        self.cleanup_old_records();
        self.push_times.len() < self.max_per_minute
    }

    /// 记录一次推送
    fn record_push(&mut self) {
        self.cleanup_old_records();
        self.push_times.push_back(chrono::Utc::now());
    }

    /// 清理1分钟前的记录
    fn cleanup_old_records(&mut self) {
        let one_minute_ago = chrono::Utc::now() - chrono::Duration::minutes(1);
        while let Some(&front_time) = self.push_times.front() {
            if front_time < one_minute_ago {
                self.push_times.pop_front();
            } else {
                break;
            }
        }
    }

    /// 获取当前推送次数
    fn current_count(&mut self) -> usize {
        self.cleanup_old_records();
        self.push_times.len()
    }

    /// 获取剩余可推送次数
    fn remaining_count(&mut self) -> usize {
        self.cleanup_old_records();
        self.max_per_minute.saturating_sub(self.push_times.len())
    }
}

/// 钉钉负载均衡器
#[derive(Debug)]
struct DingTalkLoadBalancer {
    limiters: HashMap<String, HookRateLimiter>,
    current_index: usize,
}

impl DingTalkLoadBalancer {
    fn new() -> Self {
        Self { limiters: HashMap::new(), current_index: 0 }
    }

    /// 更新 webhook 列表
    fn update_webhooks(&mut self, webhook_urls: &[String]) {
        // 移除不存在的 webhook
        self.limiters.retain(|url, _| webhook_urls.contains(url));

        // 添加新的 webhook
        for url in webhook_urls {
            if !self.limiters.contains_key(url) {
                self.limiters.insert(url.clone(), HookRateLimiter::new(url.clone()));
            }
        }

        // 重置索引
        if self.current_index >= webhook_urls.len() {
            self.current_index = 0;
        }
    }

    /// 选择一个可用的 webhook
    fn select_available_webhook(&mut self, webhook_urls: &[String]) -> Option<String> {
        if webhook_urls.is_empty() {
            return None;
        }

        // 更新 webhook 列表
        self.update_webhooks(webhook_urls);

        // 尝试找到可用的 webhook（轮询策略）
        let start_index = self.current_index;
        loop {
            let url = &webhook_urls[self.current_index];
            if let Some(limiter) = self.limiters.get_mut(url) {
                if limiter.can_push() {
                    let selected_url = url.clone();
                    self.current_index = (self.current_index + 1) % webhook_urls.len();
                    return Some(selected_url);
                }
            }

            self.current_index = (self.current_index + 1) % webhook_urls.len();

            // 如果回到起始位置，说明所有 webhook 都不可用
            if self.current_index == start_index {
                break;
            }
        }

        None
    }

    /// 记录推送
    fn record_push(&mut self, webhook_url: &str) {
        if let Some(limiter) = self.limiters.get_mut(webhook_url) {
            limiter.record_push();
        }
    }

    /// 获取所有 webhook 的状态
    fn get_status(&mut self, webhook_urls: &[String]) -> Vec<(String, usize, usize)> {
        self.update_webhooks(webhook_urls);
        webhook_urls.iter().map(|url| if let Some(limiter) = self.limiters.get_mut(url) { (url.clone(), limiter.current_count(), limiter.remaining_count()) } else { (url.clone(), 0, 20) }).collect()
    }
}

/// 纯粹的通知服务 - 完全解耦，处理各种通知渠道
#[derive(Clone)]
pub struct NotificationService {
    app_handle: AppHandle,
    channels: std::sync::Arc<tokio::sync::RwLock<HashMap<String, NotificationChannel>>>,
    dingtalk_load_balancer: Arc<RwLock<DingTalkLoadBalancer>>,
}

impl NotificationService {
    /// 创建新的通知服务实例
    pub fn new(app_handle: AppHandle) -> Self {
        Self { app_handle, channels: std::sync::Arc::new(tokio::sync::RwLock::new(HashMap::new())), dingtalk_load_balancer: Arc::new(RwLock::new(DingTalkLoadBalancer::new())) }
    }

    /// 获取app_handle的引用
    pub fn app_handle(&self) -> &AppHandle {
        &self.app_handle
    }

    /// 添加通知渠道
    pub async fn add_channel(&self, name: String, channel: NotificationChannel) -> Result<(), String> {
        let mut channels = self.channels.write().await;
        channels.insert(name, channel);
        Ok(())
    }

    /// 发送通知
    pub async fn send_notification(&self, channel_name: &str, message: NotificationMessage) -> Result<(), String> {
        let channels = self.channels.read().await;
        let channel = channels.get(channel_name).ok_or_else(|| format!("通知渠道 {} 不存在", channel_name))?;

        match channel {
            NotificationChannel::DingTalk { webhook_url } => self.send_dingtalk_notification(webhook_url, &message).await,
            NotificationChannel::Email { smtp_config } => self.send_email_notification(smtp_config, &message).await,
            NotificationChannel::WebPush { endpoint } => self.send_web_push_notification(endpoint, &message).await,
            NotificationChannel::System => self.send_system_notification(&message).await,
        }
    }

    /// 发送钉钉通知
    async fn send_dingtalk_notification(&self, webhook_url: &str, message: &NotificationMessage) -> Result<(), String> {
        let client = reqwest::Client::builder()
            .timeout(std::time::Duration::from_secs(30))
            .build()
            .map_err(|e| format!("创建HTTP客户端失败: {}", e))?;

        let payload = serde_json::json!({
            "msgtype": "text",
            "text": {
                "content": format!("{}\n{}", message.title, message.content)
            }
        });

        println!("🔔 发送钉钉通知到: {}", webhook_url);
        println!("📝 消息内容: {}", serde_json::to_string_pretty(&payload).unwrap_or_default());

        let response = client.post(webhook_url).json(&payload).send().await.map_err(|e| {
            let error_msg = format!("发送钉钉通知网络请求失败: {}", e);
            println!("❌ {}", error_msg);
            error_msg
        })?;

        let status = response.status();
        let response_text = response.text().await.unwrap_or_default();

        println!("📡 钉钉响应状态: {}", status);
        println!("📡 钉钉响应内容: {}", response_text);

        if status.is_success() {
            println!("✅ 钉钉通知发送成功");
            Ok(())
        } else {
            let error_msg = format!("钉钉通知发送失败: HTTP {} - {}", status, response_text);
            println!("❌ {}", error_msg);
            Err(error_msg)
        }
    }

    /// 发送邮件通知
    async fn send_email_notification(&self, _smtp_config: &EmailConfig, _message: &NotificationMessage) -> Result<(), String> {
        // 这里应该实现SMTP邮件发送逻辑
        // 为了简化，暂时返回成功
        Ok(())
    }

    /// 发送Web推送通知
    async fn send_web_push_notification(&self, _endpoint: &str, _message: &NotificationMessage) -> Result<(), String> {
        // 这里应该实现Web Push通知逻辑
        // 为了简化，暂时返回成功
        Ok(())
    }

    /// 发送系统通知
    async fn send_system_notification(&self, message: &NotificationMessage) -> Result<(), String> {
        // 发送到前端
        self.app_handle.emit("system_notification", message).map_err(|e| format!("发送系统通知失败: {}", e))
    }

    /// 批量发送通知
    pub async fn broadcast_notification(&self, message: NotificationMessage) -> Result<(), String> {
        let channels = self.channels.read().await;
        let channel_names: Vec<String> = channels.keys().cloned().collect();
        drop(channels);

        for channel_name in channel_names {
            if let Err(e) = self.send_notification(&channel_name, message.clone()).await {
                eprintln!("发送通知到渠道 {} 失败: {}", channel_name, e);
            }
        }

        Ok(())
    }

    /// 测试通知渠道
    pub async fn test_channel(&self, channel_name: &str) -> Result<(), String> {
        let test_message = NotificationMessage {
            title: "测试通知".to_string(),
            content: "这是一条测试通知消息".to_string(),
            level: NotificationLevel::Info,
            timestamp: chrono::Utc::now().to_rfc3339(),
            metadata: HashMap::new(),
        };

        self.send_notification(channel_name, test_message).await
    }

    /// 获取所有渠道
    pub async fn get_channels(&self) -> HashMap<String, NotificationChannel> {
        let channels = self.channels.read().await;
        channels.clone()
    }

    /// 移除通知渠道
    pub async fn remove_channel(&self, name: &str) -> Result<(), String> {
        let mut channels = self.channels.write().await;
        channels.remove(name);
        Ok(())
    }

    /// 发送持久化通知到前端（需要用户手动关闭）
    pub async fn send_persistent_notification(
        &self,
        id: &str,
        title: &str,
        message: &str,
        notification_type: Option<String>,
    ) -> Result<(), String> {
        let notification_data = serde_json::json!({
            "id": id,
            "title": title,
            "message": message,
            "type": notification_type.unwrap_or_else(|| "info".to_string()),
            "persistent": true,
            "timestamp": chrono::Utc::now().to_rfc3339()
        });

        self.app_handle
            .emit("persistent_notification", notification_data)
            .map_err(|e| format!("发送持久化通知失败: {}", e))?;

        Ok(())
    }

    /// 关闭持久化通知
    pub async fn dismiss_persistent_notification(&self, id: &str) -> Result<(), String> {
        let dismiss_data = serde_json::json!({
            "id": id,
            "action": "dismiss"
        });

        self.app_handle
            .emit("dismiss_persistent_notification", dismiss_data)
            .map_err(|e| format!("发送关闭通知事件失败: {}", e))?;

        Ok(())
    }

    /// 发送商品通知到钉钉（带频率控制）
    pub async fn send_item_notification(&self, hook_urls: &[String], item: &crate::models::config::MonitorItem) -> Result<(), String> {
        if hook_urls.is_empty() {
            return Err("没有配置钉钉 webhook".to_string());
        }

        // 选择一个可用的 webhook
        let selected_webhook = {
            let mut balancer = self.dingtalk_load_balancer.write().await;
            balancer.select_available_webhook(hook_urls)
        };

        match selected_webhook {
            Some(webhook_url) => {
                let client = reqwest::Client::builder()
                    .timeout(std::time::Duration::from_secs(30))
                    .build()
                    .map_err(|e| format!("创建HTTP客户端失败: {}", e))?;

                let payload = self.build_item_message(item);

                match client.post(&webhook_url).json(&payload).send().await {
                    Ok(response) => {
                        let status = response.status();
                        let response_text = response.text().await.unwrap_or_default();

                        if status.is_success() {
                            // 记录成功的推送
                            let mut balancer = self.dingtalk_load_balancer.write().await;
                            balancer.record_push(&webhook_url);
                            Ok(())
                        } else {
                            let error_msg = format!("钉钉商品通知发送失败: HTTP {} - {}", status, response_text);
                            println!("❌ {}", error_msg);
                            Err(error_msg)
                        }
                    }
                    Err(e) => {
                        let error_msg = format!("发送钉钉商品通知网络请求失败: {}", e);
                        println!("❌ {}", error_msg);
                        Err(error_msg)
                    }
                }
            }
            None => {
                // 所有 webhook 都达到频率限制
                Err("所有钉钉 webhook 都已达到频率限制，暂停推送".to_string())
            }
        }
    }

    /// 构建商品消息 - ActionCard 格式
    fn build_item_message(&self, item: &crate::models::config::MonitorItem) -> serde_json::Value {
        // 截取标题前200个字符
        let truncated_title = if item.title.chars().count() > 200 { format!("{}...", item.title.chars().take(200).collect::<String>()) } else { item.title.clone() };
        // 构建商品描述文本
        let description =
            format!("![图片]({}) \n #### **¥{}** {}\n**地区**: {} **时间**: {} \n ", item.pic_url, item.price, truncated_title, item.area, Self::format_timestamp_to_time(&item.publish_time));

        serde_json::json!({
            "msgtype": "actionCard",
            "actionCard": {
                "title": "新商品发现",
                "text": format!("🔔 **新商品** 发现！\n\n{}", description),
                "btnOrientation": "1",
                "btns": [
                    {
                        "title": "查看详情",
                        "actionURL": item.target_url
                    },
                    {
                        "title": "跳转下单",
                        "actionURL": format!("fleamarket://order_create?item_id={}&flutter=true", item.item_id)
                    }
                ]
            }
        })
    }

    /// 格式化时间戳为可读时间
    fn format_timestamp_to_time(timestamp: &str) -> String {
        if timestamp.is_empty() {
            return "时间未知".to_string();
        }

        // 处理13位毫秒时间戳（如 1753254931000 应该显示为 15:15:31）
        if let Ok(timestamp_num) = timestamp.parse::<i64>() {
            if let Some(datetime) = chrono::DateTime::from_timestamp(timestamp_num / 1000, 0) {
                // 转换为本地时区
                let local_datetime = datetime.with_timezone(&chrono::Local);
                return local_datetime.format("%H:%M:%S").to_string();
            }
        }

        "时间未知".to_string()
    }

    /// 获取钉钉推送状态
    pub async fn get_dingtalk_push_status(&self, hook_urls: &[String]) -> Vec<(String, usize, usize)> {
        let mut balancer = self.dingtalk_load_balancer.write().await;
        balancer.get_status(hook_urls)
    }

    /// 重置钉钉推送频率限制（用于测试或紧急情况）
    pub async fn reset_dingtalk_rate_limits(&self) {
        let mut balancer = self.dingtalk_load_balancer.write().await;
        balancer.limiters.clear();
    }

    /// 测试所有钉钉推送Hook
    pub async fn test_all_dingtalk_hooks(&self, hook_urls: &[String]) -> Result<Vec<(String, String)>, String> {
        let mut results = Vec::new();

        println!("🧪 开始测试 {} 个钉钉Hook", hook_urls.len());

        for (index, hook_url) in hook_urls.iter().enumerate() {
            println!("🔗 测试Hook #{}: {}", index + 1, hook_url);

            let test_message = serde_json::json!({
                "msgtype": "text",
                "text": {
                    "content": format!("🧪 新商品钉钉推送测试 #{}\n这是一条来自Goldfish Monitor的测试消息\n时间: {}",
                        index + 1,
                        chrono::Utc::now().format("%Y-%m-%d %H:%M:%S UTC"))
                }
            });

            println!("📝 测试消息: {}", serde_json::to_string_pretty(&test_message).unwrap_or_default());

            let client = reqwest::Client::builder()
                .timeout(std::time::Duration::from_secs(30))
                .build()
                .map_err(|e| format!("创建HTTP客户端失败: {}", e))?;

            match client.post(hook_url).json(&test_message).send().await {
                Ok(response) => {
                    let status = response.status();
                    let response_text = response.text().await.unwrap_or_default();

                    println!("📡 Hook #{} 响应状态: {}", index + 1, status);
                    println!("📡 Hook #{} 响应内容: {}", index + 1, response_text);

                    if status.is_success() {
                        println!("✅ Hook #{} 测试成功", index + 1);
                        results.push((hook_url.clone(), "成功".to_string()));
                    } else {
                        let error_msg = format!("失败: HTTP {} - {}", status, response_text);
                        println!("❌ Hook #{} 测试失败: {}", index + 1, error_msg);
                        results.push((hook_url.clone(), error_msg));
                    }
                }
                Err(e) => {
                    let error_msg = format!("失败: {}", e);
                    println!("❌ Hook #{} 网络请求失败: {}", index + 1, error_msg);
                    results.push((hook_url.clone(), error_msg));
                }
            }
        }

        println!("🏁 钉钉Hook测试完成");
        Ok(results)
    }
}
